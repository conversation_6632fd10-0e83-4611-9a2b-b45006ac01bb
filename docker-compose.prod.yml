version: '3.8'

services:
  # Nginx Reverse Proxy for Production
  nginx:
    image: nginx:alpine
    container_name: tms-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - app
      - appwrite
    networks:
      - tms-network

  # Override app service for production
  app:
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_APPWRITE_ENDPOINT=https://${DOMAIN}/v1
      - NEXT_PUBLIC_APPWRITE_PROJECT_ID=${APPWRITE_PROJECT_ID}
      - NEXT_PUBLIC_APPWRITE_DATABASE_ID=${APPWRITE_DATABASE_ID}
    ports: []  # Remove direct port exposure, use nginx instead

  # Override appwrite service for production
  appwrite:
    environment:
      - _APP_ENV=production
      - _APP_OPTIONS_FORCE_HTTPS=enabled
      - _APP_DOMAIN=https://${DOMAIN}
      - _APP_DOMAIN_TARGET=https://${DOMAIN}
    ports: []  # Remove direct port exposure, use nginx instead

  # Add SSL certificate management (optional)
  certbot:
    image: certbot/certbot
    container_name: tms-certbot
    volumes:
      - ./ssl:/etc/letsencrypt:rw
      - ./ssl-challenge:/var/www/certbot:rw
    command: certonly --webroot --webroot-path=/var/www/certbot --email ${SSL_EMAIL} --agree-tos --no-eff-email -d ${DOMAIN}
    profiles:
      - ssl-setup
