"use client";

import { useState, use<PERSON>emo, useCallback } from "react";
import { Head<PERSON> } from "@/components/layout/Header";
import { useUser } from "@/hooks/useUser";
import { useAuth } from "@/hooks/useAuth";
import { User } from "@/lib/types";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { UserDialog } from "@/components/users/UserDialog";
import { UserProfileDialog } from "@/components/users/UserProfileDialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { useToast } from "@/hooks/useToast";
import { useRouter } from "next/navigation";
import {
  UserPlus,
  Search,
  Filter,
  MoreHorizontal,
  ChevronDown,
  ChevronUp,
  Users,
  Clock,
  Building,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";

// Type for sorting options
type SortField = "name" | "email" | "role" | "department";
type SortDirection = "asc" | "desc";

// Component for the search and filter section
const SearchAndFilterBar = ({
  searchTerm,
  setSearchTerm,
  roleFilter,
  setRoleFilter,
  statusFilter,
  setStatusFilter,
  departmentFilter,
  setDepartmentFilter,
  departments,
}: {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  roleFilter: string;
  setRoleFilter: (role: string) => void;
  statusFilter: string;
  setStatusFilter: (status: string) => void;
  departmentFilter: string;
  setDepartmentFilter: (department: string) => void;
  departments: string[];
}) => (
  <div className="space-y-4 mb-6">
    <div className="flex flex-col lg:flex-row gap-4">
      <div className="relative flex-1 max-w-sm">
        <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search users by name, email, or department..."
          className="pl-10 h-10"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          aria-label="Search users"
        />
      </div>

      <div className="flex flex-wrap gap-2">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <Select
            value={roleFilter}
            onValueChange={setRoleFilter}
            aria-label="Filter by role"
          >
            <SelectTrigger className="w-36">
              <SelectValue placeholder="All Roles" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Roles</SelectItem>
              <SelectItem value="student">Students</SelectItem>
              <SelectItem value="supervisor">Supervisors</SelectItem>
              <SelectItem value="manager">Managers</SelectItem>
              <SelectItem value="admin">Admins</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Select
          value={statusFilter}
          onValueChange={setStatusFilter}
          aria-label="Filter by status"
        >
          <SelectTrigger className="w-36">
            <SelectValue placeholder="All Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="online">Online</SelectItem>
            <SelectItem value="away">Away</SelectItem>
            <SelectItem value="offline">Offline</SelectItem>
          </SelectContent>
        </Select>

        {departments.length > 0 && (
          <Select
            value={departmentFilter}
            onValueChange={setDepartmentFilter}
            aria-label="Filter by department"
          >
            <SelectTrigger className="w-40">
              <SelectValue placeholder="All Departments" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Departments</SelectItem>
              {departments.map((dept) => (
                <SelectItem key={dept} value={dept}>
                  {dept}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      </div>
    </div>
  </div>
);

export default function UsersPage() {
  const { useUsersQuery, updateUser, deleteUser, createUser } = useUser();
  const { user: currentUser } = useAuth();
  const { toast } = useToast();
  const router = useRouter();

  // Fetch users
  const { data: users = [], isLoading, error: usersError } = useUsersQuery();

  // Local state
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");
  const [departmentFilter, setDepartmentFilter] = useState("all");
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [isUserDialogOpen, setIsUserDialogOpen] = useState(false);
  const [isNewUserDialogOpen, setIsNewUserDialogOpen] = useState(false);
  const [deleteConfirmUser, setDeleteConfirmUser] = useState<User | null>(null);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [viewingUserId, setViewingUserId] = useState<string | null>(null);
  const [isProfileDialogOpen, setIsProfileDialogOpen] = useState(false);
  const [sortField, setSortField] = useState<SortField>("name");
  const [sortDirection, setSortDirection] = useState<SortDirection>("asc");
  const [isActionLoading, setIsActionLoading] = useState(false);

  // Get unique departments for filter
  const departments = useMemo(() => {
    const depts = users
      .map(user => user.department)
      .filter((dept): dept is string => Boolean(dept))
      .filter((dept, index, arr) => arr.indexOf(dept) === index)
      .sort();
    return depts;
  }, [users]);

  // Handle sorting
  const handleSort = useCallback((field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  }, [sortField, sortDirection]);

  // Memoized filtered and sorted users
  const filteredAndSortedUsers = useMemo(() => {
    // First filter
    const filtered = users.filter((user) => {
      const matchesSearch =
        user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user.department &&
          user.department.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (user.specialization &&
          user.specialization.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesRole =
        roleFilter === "all" || user.role.toLowerCase() === roleFilter;

      const matchesStatus =
        statusFilter === "all" ||
        (user.status && user.status.toLowerCase() === statusFilter) ||
        (statusFilter === "offline" && !user.status);

      const matchesDepartment =
        departmentFilter === "all" ||
        (user.department && user.department === departmentFilter);

      return matchesSearch && matchesRole && matchesStatus && matchesDepartment;
    });

    // Then sort
    return [...filtered].sort((a, b) => {
      let valueA = a[sortField] || "";
      let valueB = b[sortField] || "";

      // Handle undefined values
      if (typeof valueA === "undefined") valueA = "";
      if (typeof valueB === "undefined") valueB = "";

      // Convert to strings for comparison
      const strA = String(valueA).toLowerCase();
      const strB = String(valueB).toLowerCase();

      if (sortDirection === "asc") {
        return strA.localeCompare(strB);
      } else {
        return strB.localeCompare(strA);
      }
    });
  }, [users, searchTerm, roleFilter, statusFilter, departmentFilter, sortField, sortDirection]);

  // Handle edit user
  const handleEditUser = useCallback((user: User) => {
    setEditingUser(user);
    setIsUserDialogOpen(true);
  }, []);

  // Handle save user
  const handleSaveUser = async (userData: User) => {
    setIsActionLoading(true);
    try {
      await updateUser.mutateAsync(userData);
      setIsUserDialogOpen(false);
      setEditingUser(null);
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Failed to update user",
        description: error instanceof Error ? error.message : "Unknown error",
      });
    } finally {
      setIsActionLoading(false);
    }
  };

  // Handle delete user
  const handleDeleteUser = async (userId: string) => {
    setIsActionLoading(true);
    try {
      await deleteUser.mutateAsync(userId);
      setIsDeleteConfirmOpen(false);
      setDeleteConfirmUser(null);
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Failed to delete user",
        description: error instanceof Error ? error.message : "Unknown error",
      });
    } finally {
      setIsActionLoading(false);
    }
  };

  // Handle add user
  const handleAddUser = useCallback(() => {
    setIsNewUserDialogOpen(true);
  }, []);

  // Handle save new user
  const handleSaveNewUser = async (userData: User) => {
    setIsActionLoading(true);
    try {
      await createUser.mutateAsync(userData);
      setIsNewUserDialogOpen(false);
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Failed to create user",
        description: error instanceof Error ? error.message : "Unknown error",
      });
    } finally {
      setIsActionLoading(false);
    }
  };

  // Handle view user profile
  const handleViewUserProfile = useCallback((userId: string) => {
    // Check if the user is viewing their own profile
    const isOwnProfile = currentUser?.id === userId;

    if (isOwnProfile) {
      // If it's their own profile, navigate to the full profile page
      router.push("/profile");
    } else {
      // Otherwise, show the profile dialog
      setViewingUserId(userId);
      setIsProfileDialogOpen(true);
    }
  }, [currentUser, router]);

  // Statistics for the summary cards (moved before early return)
  const userStats = useMemo(() => {
    const totalUsers = users.length;
    const onlineUsers = users.filter(u => u.status === "online").length;
    const roleStats = users.reduce((acc, user) => {
      acc[user.role] = (acc[user.role] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total: totalUsers,
      online: onlineUsers,
      roles: roleStats,
      filtered: filteredAndSortedUsers.length
    };
  }, [users, filteredAndSortedUsers]);

  // Check if user has permission to manage users
  const canManageUsers = currentUser?.role === "admin" || currentUser?.role === "manager";

  if (!canManageUsers) {
    return (
      <div className="min-h-screen bg-background flex flex-col">
        <Header />
        <div className="flex-1 flex items-center justify-center">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Access Denied</CardTitle>
              <CardDescription>
                You don&apos;t have permission to access this page.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={() => router.push("/dashboard")}>
                Return to Dashboard
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Helper function to get status indicator
  const getStatusIndicator = (status?: string) => {
    const statusColor = status === "online"
      ? "bg-green-500"
      : status === "away"
      ? "bg-yellow-500"
      : "bg-gray-400";

    return (
      <div className="flex items-center gap-2">
        <div className={`w-2 h-2 rounded-full ${statusColor}`} />
        <span className="text-sm text-muted-foreground capitalize">
          {status || "offline"}
        </span>
      </div>
    );
  };

  // Create a component for the user table row
  const UserTableRow = ({ user }: { user: User }) => (
    <TableRow key={user.id} className="hover:bg-muted/50 transition-colors">
      <TableCell className="py-4">
        <div className="flex items-center gap-3">
          <div className="relative">
            <Avatar className="h-10 w-10">
              <AvatarImage src={user.profileImage} alt={user.name} />
              <AvatarFallback className="bg-primary/10 text-primary font-medium">
                {user.name.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            {user.status && (
              <div className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-background ${
                user.status === "online"
                  ? "bg-green-500"
                  : user.status === "away"
                  ? "bg-yellow-500"
                  : "bg-gray-400"
              }`} />
            )}
          </div>
          <div className="min-w-0 flex-1">
            <div className="font-medium text-foreground truncate">{user.name}</div>
            <div className="text-sm text-muted-foreground truncate">{user.email}</div>
          </div>
        </div>
      </TableCell>
      <TableCell className="py-4">
        <Badge
          variant="outline"
          className={`capitalize font-medium ${
            user.role === "admin"
              ? "border-red-200 bg-red-50 text-red-700 dark:border-red-800 dark:bg-red-950 dark:text-red-300"
              : user.role === "manager"
              ? "border-amber-200 bg-amber-50 text-amber-700 dark:border-amber-800 dark:bg-amber-950 dark:text-amber-300"
              : user.role === "supervisor"
              ? "border-blue-200 bg-blue-50 text-blue-700 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-300"
              : "border-green-200 bg-green-50 text-green-700 dark:border-green-800 dark:bg-green-950 dark:text-green-300"
          }`}
        >
          {user.role}
        </Badge>
      </TableCell>
      <TableCell className="py-4">
        <div className="space-y-1">
          <div className="text-sm font-medium">
            {user.department || "—"}
          </div>
          {user.specialization && (
            <div className="text-xs text-muted-foreground">
              {user.specialization}
            </div>
          )}
        </div>
      </TableCell>
      <TableCell className="py-4">
        {getStatusIndicator(user.status)}
      </TableCell>
      <TableCell className="py-4">
        {user.lastActive ? (
          <div className="text-sm text-muted-foreground">
            {formatDistanceToNow(new Date(user.lastActive), { addSuffix: true })}
          </div>
        ) : (
          <span className="text-sm text-muted-foreground">—</span>
        )}
      </TableCell>
      <TableCell className="text-right py-4">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" aria-label="User actions" className="h-8 w-8">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem
              onClick={() => handleViewUserProfile(user.id)}
            >
              View Profile
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => handleEditUser(user)}
            >
              Edit User
            </DropdownMenuItem>
            <DropdownMenuItem
              className="text-destructive focus:text-destructive"
              onClick={() => {
                setDeleteConfirmUser(user);
                setIsDeleteConfirmOpen(true);
              }}
            >
              Delete User
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </TableRow>
  );

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <Header />
      <div className="flex-1 container py-6 space-y-6">
        {/* Header Section */}
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">User Management</h1>
            <p className="text-muted-foreground mt-1">
              Manage system users and their roles
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              onClick={handleAddUser}
              className="flex items-center gap-2"
              disabled={isActionLoading}
            >
              <UserPlus className="h-4 w-4" />
              Add User
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <Users className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Users</p>
                  <p className="text-2xl font-bold">{userStats.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                  <div className="h-5 w-5 bg-green-500 rounded-full" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Online Now</p>
                  <p className="text-2xl font-bold">{userStats.online}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                  <Building className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Departments</p>
                  <p className="text-2xl font-bold">{departments.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-amber-100 dark:bg-amber-900/20 rounded-lg">
                  <Filter className="h-5 w-5 text-amber-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Filtered Results</p>
                  <p className="text-2xl font-bold">{userStats.filtered}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Users Table */}
        <Card>
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-xl">Users Directory</CardTitle>
                <CardDescription>
                  View and manage all system users
                </CardDescription>
              </div>
              <Badge variant="secondary" className="text-sm">
                {filteredAndSortedUsers.length} of {users.length} users
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <SearchAndFilterBar
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              roleFilter={roleFilter}
              setRoleFilter={setRoleFilter}
              statusFilter={statusFilter}
              setStatusFilter={setStatusFilter}
              departmentFilter={departmentFilter}
              setDepartmentFilter={setDepartmentFilter}
              departments={departments}
            />

            {isLoading ? (
              <div className="flex justify-center py-8">
                <LoadingSpinner />
              </div>
            ) : usersError ? (
              <div className="py-8 text-center text-destructive">
                <p>Failed to load users. Please try refreshing the page.</p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => window.location.reload()}
                >
                  Refresh
                </Button>
              </div>
            ) : (
              <>
                {/* Desktop Table View */}
                <div className="hidden md:block rounded-lg border bg-card overflow-hidden">
                  <Table>
                    <TableHeader>
                      <TableRow className="hover:bg-transparent border-b">
                        <TableHead
                          className="cursor-pointer hover:bg-muted/50 transition-colors font-semibold"
                          onClick={() => handleSort("name")}
                        >
                          <div className="flex items-center gap-2">
                            <Users className="h-4 w-4" />
                            User
                            {sortField === "name" && (
                              sortDirection === "asc" ?
                              <ChevronUp className="ml-1 h-4 w-4" /> :
                              <ChevronDown className="ml-1 h-4 w-4" />
                            )}
                          </div>
                        </TableHead>
                        <TableHead
                          className="cursor-pointer hover:bg-muted/50 transition-colors font-semibold"
                          onClick={() => handleSort("role")}
                        >
                          <div className="flex items-center gap-2">
                            Role
                            {sortField === "role" && (
                              sortDirection === "asc" ?
                              <ChevronUp className="ml-1 h-4 w-4" /> :
                              <ChevronDown className="ml-1 h-4 w-4" />
                            )}
                          </div>
                        </TableHead>
                        <TableHead
                          className="cursor-pointer hover:bg-muted/50 transition-colors font-semibold"
                          onClick={() => handleSort("department")}
                        >
                          <div className="flex items-center gap-2">
                            <Building className="h-4 w-4" />
                            Department
                            {sortField === "department" && (
                              sortDirection === "asc" ?
                              <ChevronUp className="ml-1 h-4 w-4" /> :
                              <ChevronDown className="ml-1 h-4 w-4" />
                            )}
                          </div>
                        </TableHead>
                        <TableHead className="font-semibold">
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-green-500 rounded-full" />
                            Status
                          </div>
                        </TableHead>
                        <TableHead className="font-semibold">
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4" />
                            Last Active
                          </div>
                        </TableHead>
                        <TableHead className="text-right font-semibold">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredAndSortedUsers.length === 0 ? (
                        <TableRow>
                          <TableCell
                            colSpan={6}
                            className="h-32 text-center"
                          >
                            <div className="flex flex-col items-center gap-2">
                              <Users className="h-8 w-8 text-muted-foreground" />
                              <p className="text-muted-foreground">
                                {searchTerm || roleFilter !== "all" || statusFilter !== "all" || departmentFilter !== "all"
                                  ? "No users match your filters."
                                  : "No users found."}
                              </p>
                              {(searchTerm || roleFilter !== "all" || statusFilter !== "all" || departmentFilter !== "all") && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    setSearchTerm("");
                                    setRoleFilter("all");
                                    setStatusFilter("all");
                                    setDepartmentFilter("all");
                                  }}
                                >
                                  Clear filters
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredAndSortedUsers.map((user) => (
                          <UserTableRow key={user.id} user={user} />
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>

                {/* Mobile Card View */}
                <div className="md:hidden space-y-4">
                  {filteredAndSortedUsers.length === 0 ? (
                    <div className="flex flex-col items-center gap-4 py-12">
                      <Users className="h-12 w-12 text-muted-foreground" />
                      <div className="text-center">
                        <p className="text-muted-foreground">
                          {searchTerm || roleFilter !== "all" || statusFilter !== "all" || departmentFilter !== "all"
                            ? "No users match your filters."
                            : "No users found."}
                        </p>
                        {(searchTerm || roleFilter !== "all" || statusFilter !== "all" || departmentFilter !== "all") && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="mt-2"
                            onClick={() => {
                              setSearchTerm("");
                              setRoleFilter("all");
                              setStatusFilter("all");
                              setDepartmentFilter("all");
                            }}
                          >
                            Clear filters
                          </Button>
                        )}
                      </div>
                    </div>
                  ) : (
                    filteredAndSortedUsers.map((user) => (
                      <Card key={user.id} className="p-4 hover:shadow-md transition-shadow">
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-3 flex-1">
                            <div className="relative">
                              <Avatar className="h-12 w-12">
                                <AvatarImage src={user.profileImage} alt={user.name} />
                                <AvatarFallback className="bg-primary/10 text-primary font-medium">
                                  {user.name.charAt(0).toUpperCase()}
                                </AvatarFallback>
                              </Avatar>
                              {user.status && (
                                <div className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-background ${
                                  user.status === "online"
                                    ? "bg-green-500"
                                    : user.status === "away"
                                    ? "bg-yellow-500"
                                    : "bg-gray-400"
                                }`} />
                              )}
                            </div>
                            <div className="min-w-0 flex-1">
                              <div className="font-medium text-foreground">{user.name}</div>
                              <div className="text-sm text-muted-foreground">{user.email}</div>
                              <div className="flex items-center gap-2 mt-1">
                                <Badge
                                  variant="outline"
                                  className={`text-xs ${
                                    user.role === "admin"
                                      ? "border-red-200 bg-red-50 text-red-700 dark:border-red-800 dark:bg-red-950 dark:text-red-300"
                                      : user.role === "manager"
                                      ? "border-amber-200 bg-amber-50 text-amber-700 dark:border-amber-800 dark:bg-amber-950 dark:text-amber-300"
                                      : user.role === "supervisor"
                                      ? "border-blue-200 bg-blue-50 text-blue-700 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-300"
                                      : "border-green-200 bg-green-50 text-green-700 dark:border-green-800 dark:bg-green-950 dark:text-green-300"
                                  }`}
                                >
                                  {user.role}
                                </Badge>
                                {getStatusIndicator(user.status)}
                              </div>
                              {user.department && (
                                <div className="text-xs text-muted-foreground mt-1">
                                  {user.department}
                                  {user.specialization && ` • ${user.specialization}`}
                                </div>
                              )}
                              {user.lastActive && (
                                <div className="text-xs text-muted-foreground mt-1">
                                  Last active {formatDistanceToNow(new Date(user.lastActive), { addSuffix: true })}
                                </div>
                              )}
                            </div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon" className="h-8 w-8">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                onClick={() => handleViewUserProfile(user.id)}
                              >
                                View Profile
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleEditUser(user)}
                              >
                                Edit User
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                className="text-destructive focus:text-destructive"
                                onClick={() => {
                                  setDeleteConfirmUser(user);
                                  setIsDeleteConfirmOpen(true);
                                }}
                              >
                                Delete User
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </Card>
                    ))
                  )}
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Edit User Dialog */}
        {editingUser && (
          <Dialog open={isUserDialogOpen} onOpenChange={setIsUserDialogOpen}>
            <UserDialog
              user={editingUser}
              onSave={handleSaveUser}
              onClose={() => {
                setIsUserDialogOpen(false);
                setEditingUser(null);
              }}
            />
          </Dialog>
        )}

        {/* New User Dialog */}
        <Dialog open={isNewUserDialogOpen} onOpenChange={setIsNewUserDialogOpen}>
          <UserDialog
            isNew={true}
            onSave={handleSaveNewUser}
            onClose={() => setIsNewUserDialogOpen(false)}
          />
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <Dialog open={isDeleteConfirmOpen} onOpenChange={setIsDeleteConfirmOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Confirm Deletion</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete {deleteConfirmUser?.name}? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsDeleteConfirmOpen(false)}
                disabled={isActionLoading}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={() => deleteConfirmUser && handleDeleteUser(deleteConfirmUser.id)}
                disabled={isActionLoading}
              >
                {isActionLoading ? <LoadingSpinner className="h-4 w-4 mr-2" /> : null}
                Delete
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* User Profile Dialog */}
        {viewingUserId && (
          <UserProfileDialog
            userId={viewingUserId}
            isOpen={isProfileDialogOpen}
            onClose={() => {
              setIsProfileDialogOpen(false);
              setViewingUserId(null);
            }}
            onEdit={(user) => {
              // Close profile dialog and open edit dialog
              setIsProfileDialogOpen(false);
              handleEditUser(user);
            }}
          />
        )}
      </div>
    </div>
  );
}
