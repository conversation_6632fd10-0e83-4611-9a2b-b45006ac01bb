import { Project } from '@/lib/types';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { Progress } from '@/components/ui/progress';
import { FileText, Calendar, Users, Clock } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface ProjectCardProps {
  project: Project;
  student?: { id: string; name: string; profileImage?: string };
  onClick?: () => void;
  showProgress?: boolean;
}

export function ProjectCard({ project, student, onClick, showProgress = true }: ProjectCardProps) {
  const progress = project.documents?.length > 0
    ? Math.round((project.documents.filter(d => typeof d === 'object' ? d.status === 'approved' : false).length / project.documents.length) * 100)
    : 0;

  const deadline = project.deadline ? new Date(project.deadline) : null;
  const isOverdue = deadline && deadline < new Date();

  return (
    <Card 
      className="hover:bg-accent/50 transition-all duration-200 cursor-pointer overflow-hidden border-l-4 group h-full flex flex-col"
      style={{ borderLeftColor: getStatusColor(project.status) }}
      onClick={onClick}
    >
      <CardHeader className="pb-2 space-y-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg line-clamp-1 group-hover:text-primary transition-colors">
            {project.title}
          </CardTitle>
          <Badge className={`${getStatusBgColor(project.status)} text-white`}>
            {formatStatus(project.status)}
          </Badge>
        </div>
        
        {student && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Avatar className="h-5 w-5">
              <AvatarImage src={student.profileImage} alt={student.name} />
              <AvatarFallback>{student.name?.slice(0, 2).toUpperCase()}</AvatarFallback>
            </Avatar>
            <span className="line-clamp-1">{student.name}</span>
          </div>
        )}
      </CardHeader>
      
      <CardContent className="flex-1">
        <p className="text-sm text-muted-foreground line-clamp-2 mb-4">
          {project.description}
        </p>
        
        {showProgress && (
          <div className="space-y-2 mb-4">
            <div className="flex justify-between text-xs">
              <span className="text-muted-foreground">Progress</span>
              <span className="font-medium">{progress}%</span>
            </div>
            <Progress 
              value={progress} 
              className={`h-1.5 ${progress === 100 ? "[&>.bg-primary]:bg-green-500" : ""}`}
            />
          </div>
        )}
        
        <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
          {deadline && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    <span className={isOverdue ? "text-red-500 font-medium" : ""}>
                      {format(deadline, 'MMM d, yyyy')}
                    </span>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{isOverdue ? 'Overdue' : 'Deadline'}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
          
          <div className="flex items-center gap-1">
            <FileText className="h-3 w-3" />
            <span>{project.documents?.length || 0} docs</span>
          </div>
        </div>
      </CardContent>
      
      <CardFooter className="pt-2 pb-3 border-t text-xs text-muted-foreground">
        <div className="flex justify-between items-center w-full">
          <div className="flex items-center gap-1">
            <Users className="h-3 w-3" />
            <span>{project.supervisorIds?.length || 0} supervisors</span>
          </div>
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            <span>{format(new Date(project.lastActivity), 'MMM d')}</span>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}

function getStatusColor(status: string): string {
  switch (status) {
    case 'active': return '#10b981'; // green-500
    case 'completed': return '#3b82f6'; // blue-500
    case 'archived': return '#6b7280'; // gray-500
    case 'suspended': return '#ef4444'; // red-500
    case 'pending_supervisor': return '#f59e0b'; // amber-500
    default: return '#6b7280'; // gray-500
  }
}

function getStatusBgColor(status: string): string {
  switch (status) {
    case 'active': return 'bg-green-500 hover:bg-green-600';
    case 'completed': return 'bg-blue-500 hover:bg-blue-600';
    case 'archived': return 'bg-gray-500 hover:bg-gray-600';
    case 'suspended': return 'bg-red-500 hover:bg-red-600';
    case 'pending_supervisor': return 'bg-amber-500 hover:bg-amber-600';
    default: return 'bg-gray-500 hover:bg-gray-600';
  }
}

function formatStatus(status: string): string {
  return status.split('_').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ');
}


