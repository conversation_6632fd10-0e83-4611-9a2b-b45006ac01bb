"use client";

import { Project, ProjectMilestone } from "@/lib/types";
import { format } from "date-fns";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar, Clock, Target } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { useRouter } from "next/navigation";
import { memo, useMemo } from "react";

// Unified deadline interface for both projects and milestones
interface DeadlineItem {
  id: string;
  title: string;
  date: Date;
  type: 'project' | 'milestone';
  status: 'overdue' | 'upcoming';
  projectId: string;
  projectTitle: string;
  description?: string;
}

interface UpcomingDeadlinesCardProps {
  projects: Project[];
  isLoading?: boolean;
}

export const UpcomingDeadlinesCard = memo(function UpcomingDeadlinesCard({
  projects,
  isLoading = false
}: UpcomingDeadlinesCardProps) {
  const router = useRouter();

  // Process projects and milestones into unified deadline items
  const deadlineItems = useMemo(() => {
    const items: DeadlineItem[] = [];
    const now = new Date();

    projects.forEach((project: Project) => {
      // Add project deadline
      if (project.deadline) {
        const deadline = new Date(project.deadline);
        const isOverdue = deadline < now && project.status !== 'completed';

        items.push({
          id: `project-${project.id}`,
          title: project.title,
          date: deadline,
          type: 'project',
          status: isOverdue ? 'overdue' : 'upcoming',
          projectId: project.id,
          projectTitle: project.title,
          description: 'Project deadline'
        });
      }

      // Add milestone deadlines
      if (project.milestones && project.milestones.length > 0) {
        project.milestones.forEach((milestone: ProjectMilestone) => {
          if (!milestone.completed) {
            const milestoneDate = new Date(milestone.dueDate);
            const isOverdue = milestoneDate < now;

            items.push({
              id: `milestone-${milestone.id}`,
              title: milestone.title,
              date: milestoneDate,
              type: 'milestone',
              status: isOverdue ? 'overdue' : 'upcoming',
              projectId: project.id,
              projectTitle: project.title,
              description: milestone.description || 'Project milestone'
            });
          }
        });
      }
    });

    // Sort by date (earliest first) and limit to 10 items
    return items
      .sort((a, b) => a.date.getTime() - b.date.getTime())
      .slice(0, 10);
  }, [projects]);

  const getDeadlineIcon = (type: DeadlineItem['type']) => {
    return type === 'project' ?
      <Clock className="h-4 w-4 text-muted-foreground" /> :
      <Target className="h-4 w-4 text-muted-foreground" />;
  };

  const handleItemClick = (item: DeadlineItem) => {
    router.push(`/projects/${item.projectId}`);
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <Skeleton className="h-6 w-40" />
              <Skeleton className="h-4 w-32 mt-1" />
            </div>
            <Skeleton className="h-9 w-28" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array(3).fill(0).map((_, i) => (
              <div key={i} className="flex items-center gap-4 p-4 rounded-lg border">
                <div className="flex-1 min-w-0">
                  <Skeleton className="h-5 w-3/4" />
                  <div className="flex items-center gap-2 mt-1">
                    <Skeleton className="h-4 w-4 rounded-full" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                </div>
                <Skeleton className="h-6 w-20" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Upcoming Deadlines</CardTitle>
            <CardDescription>Projects and milestones due soon</CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push("/calendar")}
          >
            View Calendar
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[400px] pr-4">
          <div className="space-y-4">
            {deadlineItems.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No upcoming deadlines
              </div>
            ) : (
              deadlineItems.map((item) => (
                <div
                  key={item.id}
                  className="flex items-center gap-4 p-4 rounded-lg border bg-card hover:bg-accent/5 transition-colors cursor-pointer"
                  onClick={() => handleItemClick(item)}
                >
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      {getDeadlineIcon(item.type)}
                      <p className="font-medium truncate">{item.title}</p>
                    </div>
                    {item.type === 'milestone' && (
                      <p className="text-xs text-muted-foreground mb-1">
                        Project: {item.projectTitle}
                      </p>
                    )}
                    <div className="flex items-center gap-2">
                      <Calendar className="h-3 w-3 text-muted-foreground" />
                      <p className="text-sm text-muted-foreground">
                        Due {format(item.date, "PPP")}
                      </p>
                    </div>
                  </div>
                  <div className="flex flex-col items-end gap-1">
                    <Badge variant={item.status === 'overdue' ? "destructive" : "default"}>
                      {item.status === 'overdue' ? "Overdue" : "Upcoming"}
                    </Badge>
                    <span className="text-xs text-muted-foreground capitalize">
                      {item.type}
                    </span>
                  </div>
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
});
