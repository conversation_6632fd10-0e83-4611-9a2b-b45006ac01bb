'use client';

import { useState } from 'react';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isSameDay, isToday, addMonths, subMonths } from 'date-fns';
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon, Clock, FileText, Target } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { Header } from '@/components/layout/Header';
import { useAuth } from '@/hooks/useAuth';
import { useProject } from '@/hooks/useProject';
import { useDocument } from '@/hooks/useDocument';
import { cn } from '@/lib/utils';
import { Project, ProjectMilestone, Document } from '@/lib/types';

interface CalendarEvent {
  id: string;
  title: string;
  date: Date;
  type: 'deadline' | 'milestone' | 'document';
  status: 'completed' | 'pending' | 'overdue';
  projectId?: string;
  projectTitle?: string;
  description?: string;
}

export default function CalendarPage() {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const { user } = useAuth();
  const { useProjectsQuery } = useProject();
  const { data: projects, isLoading: projectsLoading } = useProjectsQuery();
  const { getList } = useDocument();
  const { data: documents, isLoading: documentsLoading } = getList();

  // Generate calendar events from projects, milestones, and documents
  const calendarEvents: CalendarEvent[] = [];

  if (projects) {
    projects.forEach((project: Project) => {
      // Add project deadlines
      if (project.deadline) {
        const isOverdue = new Date(project.deadline) < new Date() && project.status !== 'completed';
        calendarEvents.push({
          id: `project-${project.id}`,
          title: project.title,
          date: new Date(project.deadline),
          type: 'deadline',
          status: project.status === 'completed' ? 'completed' : isOverdue ? 'overdue' : 'pending',
          projectId: project.id,
          projectTitle: project.title,
          description: `Project deadline`
        });
      }

      // Add milestones
      if (project.milestones) {
        project.milestones.forEach((milestone: ProjectMilestone) => {
          const isOverdue = new Date(milestone.dueDate) < new Date() && !milestone.completed;
          const description = milestone.description
            ? `${milestone.description}`
            : `Project milestone for ${project.title}`;

          calendarEvents.push({
            id: `milestone-${milestone.id}`,
            title: milestone.title,
            date: new Date(milestone.dueDate),
            type: 'milestone',
            status: milestone.completed ? 'completed' : isOverdue ? 'overdue' : 'pending',
            projectId: project.id,
            projectTitle: project.title,
            description: description
          });
        });
      }
    });
  }

  if (documents) {
    documents.forEach((document: Document) => {
      calendarEvents.push({
        id: `document-${document.id}`,
        title: document.title,
        date: new Date(document.createdAt),
        type: 'document',
        status: 'completed',
        projectId: document.projectId,
        description: `Document submitted: ${document.type}`
      });
    });
  }

  const monthStart = startOfMonth(currentDate);
  const monthEnd = endOfMonth(currentDate);
  const monthDays = eachDayOfInterval({ start: monthStart, end: monthEnd });

  const getEventsForDate = (date: Date) => {
    return calendarEvents.filter(event => isSameDay(event.date, date));
  };

  const selectedDateEvents = selectedDate ? getEventsForDate(selectedDate) : [];

  const getEventTypeIcon = (type: CalendarEvent['type']) => {
    switch (type) {
      case 'deadline':
        return <Clock className="h-4 w-4" />;
      case 'milestone':
        return <Target className="h-4 w-4" />;
      case 'document':
        return <FileText className="h-4 w-4" />;
    }
  };

  const getEventTypeColor = (event: CalendarEvent) => {
    if (event.status === 'overdue') return 'destructive';
    if (event.status === 'completed') return 'secondary';
    
    switch (event.type) {
      case 'deadline':
        return 'default';
      case 'milestone':
        return 'outline';
      case 'document':
        return 'secondary';
      default:
        return 'default';
    }
  };

  if (projectsLoading || documentsLoading) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="container mx-auto p-6 space-y-6">
          <div className="flex items-center justify-between">
            <Skeleton className="h-8 w-32" />
            <div className="flex gap-2">
              <Skeleton className="h-10 w-10" />
              <Skeleton className="h-10 w-32" />
              <Skeleton className="h-10 w-10" />
            </div>
          </div>
          <div className="grid gap-6 md:grid-cols-3">
            <div className="md:col-span-2">
              <Skeleton className="h-96 w-full" />
            </div>
            <div>
              <Skeleton className="h-96 w-full" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Calendar</h1>
          <p className="text-muted-foreground">
            View project deadlines, milestones, and important dates
          </p>
        </div>
        
        {/* Month Navigation */}
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="icon"
            onClick={() => setCurrentDate(subMonths(currentDate, 1))}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          
          <div className="min-w-[200px] text-center">
            <h2 className="text-xl font-semibold">
              {format(currentDate, 'MMMM yyyy')}
            </h2>
          </div>
          
          <Button
            variant="outline"
            size="icon"
            onClick={() => setCurrentDate(addMonths(currentDate, 1))}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Event Legend */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-6 text-sm">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-blue-600" />
              <span>Project Deadlines</span>
            </div>
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-purple-600" />
              <span>Milestones</span>
            </div>
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-green-600" />
              <span>Document Submissions</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-red-100 border border-red-300 rounded"></div>
              <span>Overdue</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-100 border border-blue-300 rounded"></div>
              <span>Pending</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-100 border border-green-300 rounded"></div>
              <span>Completed</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Calendar Grid */}
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CalendarIcon className="h-5 w-5" />
                {format(currentDate, 'MMMM yyyy')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {/* Calendar Header */}
              <div className="grid grid-cols-7 gap-1 mb-4">
                {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                  <div key={day} className="p-2 text-center text-sm font-medium text-muted-foreground">
                    {day}
                  </div>
                ))}
              </div>

              {/* Calendar Days */}
              <div className="grid grid-cols-7 gap-1">
                {monthDays.map((day) => {
                  const dayEvents = getEventsForDate(day);
                  const isSelected = selectedDate && isSameDay(day, selectedDate);
                  const isCurrentDay = isToday(day);
                  
                  return (
                    <button
                      key={day.toISOString()}
                      onClick={() => setSelectedDate(day)}
                      className={cn(
                        "p-2 min-h-[80px] text-left border rounded-lg hover:bg-accent transition-colors",
                        isSelected && "bg-primary text-primary-foreground",
                        isCurrentDay && !isSelected && "bg-accent",
                        !isSameMonth(day, currentDate) && "text-muted-foreground opacity-50"
                      )}
                    >
                      <div className="text-sm font-medium mb-1">
                        {format(day, 'd')}
                      </div>
                      <div className="space-y-1">
                        {dayEvents.slice(0, 2).map((event) => (
                          <div
                            key={event.id}
                            className={cn(
                              "text-xs p-1 rounded truncate",
                              event.status === 'overdue' && "bg-red-100 text-red-800",
                              event.status === 'completed' && "bg-green-100 text-green-800",
                              event.status === 'pending' && "bg-blue-100 text-blue-800"
                            )}
                          >
                            {event.title}
                          </div>
                        ))}
                        {dayEvents.length > 2 && (
                          <div className="text-xs text-muted-foreground">
                            +{dayEvents.length - 2} more
                          </div>
                        )}
                      </div>
                    </button>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Event Details Sidebar */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>
                {selectedDate ? format(selectedDate, 'MMMM d, yyyy') : 'Select a date'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[400px]">
                {selectedDateEvents.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    {selectedDate ? 'No events on this date' : 'Click on a date to view events'}
                  </div>
                ) : (
                  <div className="space-y-4">
                    {selectedDateEvents.map((event) => (
                      <div
                        key={event.id}
                        className="p-4 border rounded-lg space-y-2"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-2">
                            {getEventTypeIcon(event.type)}
                            <h4 className="font-medium">{event.title}</h4>
                          </div>
                          <Badge variant={getEventTypeColor(event)}>
                            {event.status}
                          </Badge>
                        </div>
                        
                        {event.projectTitle && (
                          <p className="text-sm text-muted-foreground">
                            Project: {event.projectTitle}
                          </p>
                        )}
                        
                        {event.description && (
                          <p className="text-sm text-muted-foreground">
                            {event.description}
                          </p>
                        )}
                        
                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                          <CalendarIcon className="h-3 w-3" />
                          {format(event.date, 'PPP')}
                        </div>

                        {event.type === 'milestone' && (
                          <div className="text-xs text-muted-foreground">
                            {event.status === 'completed' ? '✅ Completed' :
                             event.status === 'overdue' ? '⚠️ Overdue' :
                             '📅 Due'}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>
        </div>
      </div>
      </div>
    </div>
  );
}
