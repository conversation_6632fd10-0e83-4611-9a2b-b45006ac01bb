version: '3.8'

services:
  # Next.js Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: tms-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_APPWRITE_ENDPOINT=http://localhost:8080/v1
      - NEXT_PUBLIC_APPWRITE_PROJECT_ID=${APPWRITE_PROJECT_ID}
      - NEXT_PUBLIC_APPWRITE_DATABASE_ID=${APPWRITE_DATABASE_ID}
    depends_on:
      - appwrite
    networks:
      - tms-network

  # Appwrite Backend Services
  appwrite:
    image: appwrite/appwrite:1.4.13
    container_name: appwrite
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      - _APP_ENV=production
      - _APP_LOCALE=en
      - _APP_CONSOLE_WHITELIST_ROOT=enabled
      - _APP_CONSOLE_WHITELIST_EMAILS=
      - _APP_CONSOLE_WHITELIST_IPS=
      - _APP_SYSTEM_EMAIL_NAME=${_APP_SYSTEM_EMAIL_NAME}
      - _APP_SYSTEM_EMAIL_ADDRESS=${_APP_SYSTEM_EMAIL_ADDRESS}
      - _APP_SYSTEM_SECURITY_EMAIL_ADDRESS=${_APP_SYSTEM_SECURITY_EMAIL_ADDRESS}
      - _APP_SYSTEM_RESPONSE_FORMAT=
      - _APP_OPTIONS_ABUSE=enabled
      - _APP_OPTIONS_FORCE_HTTPS=disabled
      - _APP_OPENSSL_KEY_V1=${_APP_OPENSSL_KEY_V1}
      - _APP_DOMAIN=${_APP_DOMAIN}
      - _APP_DOMAIN_TARGET=${_APP_DOMAIN_TARGET}
      - _APP_REDIS_HOST=redis
      - _APP_REDIS_PORT=6379
      - _APP_REDIS_USER=
      - _APP_REDIS_PASS=
      - _APP_DB_HOST=mariadb
      - _APP_DB_PORT=3306
      - _APP_DB_SCHEMA=appwrite
      - _APP_DB_USER=${_APP_DB_USER}
      - _APP_DB_PASS=${_APP_DB_PASS}
      - _APP_SMTP_HOST=${_APP_SMTP_HOST}
      - _APP_SMTP_PORT=${_APP_SMTP_PORT}
      - _APP_SMTP_SECURE=${_APP_SMTP_SECURE}
      - _APP_SMTP_USERNAME=${_APP_SMTP_USERNAME}
      - _APP_SMTP_PASSWORD=${_APP_SMTP_PASSWORD}
      - _APP_USAGE_STATS=disabled
      - _APP_INFLUXDB_HOST=influxdb
      - _APP_INFLUXDB_PORT=8086
      - _APP_STORAGE_LIMIT=30000000
      - _APP_STORAGE_PREVIEW_LIMIT=20000000
      - _APP_STORAGE_ANTIVIRUS=disabled
      - _APP_STORAGE_ANTIVIRUS_HOST=clamav
      - _APP_STORAGE_ANTIVIRUS_PORT=3310
      - _APP_STORAGE_DEVICE=local
      - _APP_STORAGE_S3_ACCESS_KEY=
      - _APP_STORAGE_S3_SECRET=
      - _APP_STORAGE_S3_REGION=
      - _APP_STORAGE_S3_BUCKET=
      - _APP_STORAGE_DO_SPACES_ACCESS_KEY=
      - _APP_STORAGE_DO_SPACES_SECRET=
      - _APP_STORAGE_DO_SPACES_REGION=
      - _APP_STORAGE_DO_SPACES_BUCKET=
      - _APP_STORAGE_BACKBLAZE_ACCESS_KEY=
      - _APP_STORAGE_BACKBLAZE_SECRET=
      - _APP_STORAGE_BACKBLAZE_REGION=
      - _APP_STORAGE_BACKBLAZE_BUCKET=
      - _APP_STORAGE_LINODE_ACCESS_KEY=
      - _APP_STORAGE_LINODE_SECRET=
      - _APP_STORAGE_LINODE_REGION=
      - _APP_STORAGE_LINODE_BUCKET=
      - _APP_STORAGE_WASABI_ACCESS_KEY=
      - _APP_STORAGE_WASABI_SECRET=
      - _APP_STORAGE_WASABI_REGION=
      - _APP_STORAGE_WASABI_BUCKET=
      - _APP_FUNCTIONS_SIZE_LIMIT=30000000
      - _APP_FUNCTIONS_TIMEOUT=900
      - _APP_FUNCTIONS_BUILD_TIMEOUT=900
      - _APP_FUNCTIONS_CONTAINERS=10
      - _APP_FUNCTIONS_CPUS=0
      - _APP_FUNCTIONS_MEMORY=0
      - _APP_FUNCTIONS_MEMORY_SWAP=0
      - _APP_FUNCTIONS_RUNTIMES=node-16.0,php-8.0,python-3.9,ruby-3.0
      - _APP_EXECUTOR_SECRET=${_APP_EXECUTOR_SECRET}
      - _APP_EXECUTOR_HOST=http://appwrite-executor/v1
      - _APP_LOGGING_PROVIDER=
      - _APP_LOGGING_CONFIG=
      - _APP_MAINTENANCE_INTERVAL=86400
      - _APP_MAINTENANCE_RETENTION_EXECUTION=1209600
      - _APP_MAINTENANCE_RETENTION_CACHE=2592000
      - _APP_MAINTENANCE_RETENTION_ABUSE=86400
      - _APP_MAINTENANCE_RETENTION_AUDIT=1209600
    volumes:
      - appwrite-uploads:/storage/uploads:rw
      - appwrite-cache:/storage/cache:rw
      - appwrite-config:/storage/config:rw
      - appwrite-certificates:/storage/certificates:rw
      - appwrite-functions:/storage/functions:rw
    depends_on:
      - mariadb
      - redis
    networks:
      - tms-network

  # MariaDB Database
  mariadb:
    image: mariadb:10.7
    container_name: appwrite-mariadb
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=${_APP_DB_ROOT_PASS}
      - MYSQL_DATABASE=appwrite
      - MYSQL_USER=${_APP_DB_USER}
      - MYSQL_PASSWORD=${_APP_DB_PASS}
      - MYSQL_CHARSET=utf8mb4
      - MYSQL_COLLATION=utf8mb4_unicode_ci
    volumes:
      - appwrite-mariadb:/var/lib/mysql:rw
    command: 'mysqld --innodb-flush-method=fsync'
    networks:
      - tms-network

  # Redis Cache
  redis:
    image: redis:7.0-alpine
    container_name: appwrite-redis
    restart: unless-stopped
    command: redis-server --save 20 1 --loglevel warning
    volumes:
      - appwrite-redis:/data:rw
    networks:
      - tms-network

  # InfluxDB for Usage Stats (Optional)
  influxdb:
    image: appwrite/influxdb:1.5.0
    container_name: appwrite-influxdb
    restart: unless-stopped
    environment:
      - INFLUXDB_HTTP_FLUX_ENABLED=false
      - INFLUXDB_HTTP_LOG_ENABLED=false
      - INFLUXDB_DATA_QUERY_LOG_ENABLED=false
    volumes:
      - appwrite-influxdb:/var/lib/influxdb:rw
    networks:
      - tms-network

volumes:
  appwrite-mariadb:
  appwrite-redis:
  appwrite-cache:
  appwrite-uploads:
  appwrite-certificates:
  appwrite-functions:
  appwrite-influxdb:
  appwrite-config:

networks:
  tms-network:
    driver: bridge
