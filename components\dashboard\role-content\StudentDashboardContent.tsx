"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { DocumentWorkflowStatus } from "@/components/documents/DocumentWorkflowStatus";
import { Button } from "@/components/ui/button";
import { FileText, Calendar, Clock, ChevronRight } from "lucide-react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { Project, Document, User, DocumentStatus, ProjectMilestone } from "@/lib/types";

interface StudentDashboardContentProps {
  projects: Project[];
  documents: Document[];
  supervisors: User[];
  isLoading: boolean;
}

// Helper function to safely format dates
const safeFormatDate = (dateValue: string | Date | undefined, formatString = "PP") => {
  if (!dateValue) return "Never";
  try {
    return format(new Date(dateValue), formatString);
  } catch (error) {
    console.warn("Invalid date format:", dateValue);
    return "Invalid date";
  }
};

// Helper function to find the next deadline (project or milestone)
const getNextDeadline = (projects: Project[]) => {
  const now = new Date();
  const upcomingDeadlines: Array<{
    date: Date;
    title: string;
    type: 'project' | 'milestone';
    projectTitle?: string;
  }> = [];

  projects.forEach((project) => {
    // Add project deadline
    if (project.deadline) {
      const deadline = new Date(project.deadline);
      if (deadline >= now && project.status !== 'completed') {
        upcomingDeadlines.push({
          date: deadline,
          title: project.title,
          type: 'project'
        });
      }
    }

    // Add milestone deadlines
    if (project.milestones && project.milestones.length > 0) {
      project.milestones.forEach((milestone: ProjectMilestone) => {
        if (!milestone.completed) {
          const milestoneDate = new Date(milestone.dueDate);
          if (milestoneDate >= now) {
            upcomingDeadlines.push({
              date: milestoneDate,
              title: milestone.title,
              type: 'milestone',
              projectTitle: project.title
            });
          }
        }
      });
    }
  });

  // Sort by date and return the earliest
  upcomingDeadlines.sort((a, b) => a.date.getTime() - b.date.getTime());
  return upcomingDeadlines[0] || null;
};

// Document card component
const DocumentCard = ({ doc, onClick }: { doc: Document; onClick: () => void }) => (
  <div
    className="flex items-center gap-4 p-3 rounded-lg border bg-card hover:bg-accent/5 transition-colors cursor-pointer"
    onClick={onClick}
  >
    <FileText className="h-10 w-10 text-primary/70" />
    <div className="flex-1 min-w-0">
      <p className="font-medium truncate">{doc.title}</p>
      <p className="text-sm text-muted-foreground">
        Last updated: {safeFormatDate(doc.lastModified)}
      </p>
    </div>
    <div className="flex flex-col items-end gap-1">
      <DocumentWorkflowStatus
        currentStatus={doc.status as DocumentStatus}
        compact={true}
      />
    </div>
  </div>
);

export function StudentDashboardContent({
  projects,
  documents,
  supervisors,
  isLoading
}: StudentDashboardContentProps) {
  const router = useRouter();

  // Get the next deadline (project or milestone)
  const nextDeadline = getNextDeadline(projects);

  return (
    <div className="space-y-6">
      {/* Student-specific stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Current Projects</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{projects.length}</div>
            <p className="text-xs text-muted-foreground">
              {projects.filter(p => p.status === "active").length} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Documents</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{documents.length}</div>
            <p className="text-xs text-muted-foreground">
              {documents.filter(d => d.status === "approved").length} approved
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Next Deadline</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {nextDeadline ? (
              <>
                <div className="text-2xl font-bold">
                  {safeFormatDate(nextDeadline.date, "MMM d")}
                </div>
                <p className="text-xs text-muted-foreground">
                  {nextDeadline.title}
                  {nextDeadline.type === 'milestone' && nextDeadline.projectTitle && (
                    <span className="block">Project: {nextDeadline.projectTitle}</span>
                  )}
                </p>
                <p className="text-xs text-muted-foreground mt-1 capitalize">
                  {nextDeadline.type}
                </p>
              </>
            ) : (
              <>
                <div className="text-2xl font-bold">-</div>
                <p className="text-xs text-muted-foreground">No upcoming deadlines</p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Recent Documents */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Documents</CardTitle>
          <CardDescription>Your latest submissions and drafts</CardDescription>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[200px]">
            <div className="space-y-4">
              {documents.length > 0 ? (
                documents.slice(0, 5).map((doc) => (
                  <DocumentCard
                    key={doc.id}
                    doc={doc}
                    onClick={() => router.push(`/documents/${doc.id}`)}
                  />
                ))
              ) : (
                <p className="text-center text-muted-foreground py-4">No documents found</p>
              )}
            </div>
          </ScrollArea>
          {documents.length > 0 && (
            <Button
              variant="outline"
              className="w-full mt-4"
              onClick={() => router.push('/documents')}
            >
              View All Documents
              <ChevronRight className="ml-2 h-4 w-4" />
            </Button>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
