'use client';

/**
 * Reviews Page - Refined Implementation
 *
 * Key Refinements:
 * 1. Removed non-existent getProjectsByUserId import
 * 2. Replaced inefficient data fetching with useProjectsQuery hook
 * 3. Optimized performance with memoized filtering and proper query dependencies
 * 4. Enhanced UI with project information display and document type
 * 5. Improved error handling and loading states
 * 6. Consistent with codebase patterns and established hooks
 */

import { useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { useQuery } from '@tanstack/react-query';
import { useProject } from '@/hooks/useProject';
import { getDocuments } from '@/lib/api/documents';
import { Document, DocumentStatus, Project } from '@/lib/types';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Clock, CheckCircle, AlertCircle } from 'lucide-react';
import { format } from 'date-fns';
import { Header } from '@/components/layout/Header';

export default function ReviewsPage() {
  const { user } = useAuth();
  const router = useRouter();
  const { useProjectsQuery } = useProject();
  const [activeTab, setActiveTab] = useState<string>('pending');

  // Redirect if not a supervisor
  useEffect(() => {
    if (user && user.role !== 'supervisor') {
      router.push('/dashboard');
    }
  }, [user, router]);

  // Fetch projects where the supervisor is a team member
  const { data: supervisorProjects = [], isLoading: projectsLoading } = useProjectsQuery(
    undefined, // No filters - we'll filter client-side for team membership
    {
      enabled: !!user?.id && user.role === 'supervisor',
      select: (projects: Project[]) =>
        projects.filter(project =>
          project.teamMembers?.includes(user?.id || '') ||
          project.supervisorIds?.includes(user?.id || '')
        )
    }
  );

  // Fetch documents that the supervisor can review
  const { data: documents = [], isLoading: documentsLoading, error } = useQuery({
    queryKey: ['documents', 'supervisor-reviews', user?.id],
    queryFn: async () => {
      if (!user?.id || user.role !== 'supervisor') return [];

      // Get all documents
      const allDocuments = await getDocuments();

      // Filter documents from supervisor's projects
      const supervisorDocuments = allDocuments.filter(doc =>
        supervisorProjects.some(project => project.id === doc.projectId)
      );

      return supervisorDocuments;
    },
    enabled: !!user?.id && user.role === 'supervisor' && !projectsLoading && supervisorProjects.length >= 0,
  });

  const isLoading = projectsLoading || documentsLoading;

  // Filter documents by status with memoization for performance
  const { pendingReviews, completedReviews, rejectedReviews } = useMemo(() => {
    return {
      pendingReviews: documents.filter(doc => doc.status === 'under_review'),
      completedReviews: documents.filter(doc => doc.status === 'approved'),
      rejectedReviews: documents.filter(doc => doc.status === 'rejected')
    };
  }, [documents]);

  // Navigate to document review page
  const handleReviewDocument = (documentId: string) => {
    router.push(`/documents/${documentId}`);
  };

  // Get status badge
  const getStatusBadge = (status: DocumentStatus) => {
    switch (status) {
      case 'draft':
        return <Badge variant="outline" className="bg-gray-100 text-gray-800">Draft</Badge>;
      case 'under_review':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800">Under Review</Badge>;
      case 'approved':
        return <Badge variant="outline" className="bg-green-100 text-green-800">Approved</Badge>;
      case 'rejected':
        return <Badge variant="outline" className="bg-red-100 text-red-800">Rejected</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Render document card with enhanced project information
  const renderDocumentCard = (document: Document) => {
    const project = supervisorProjects.find(p => p.id === document.projectId);

    return (
      <Card key={document.id} className="mb-4 hover:shadow-md transition-shadow">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start">
            <CardTitle className="text-lg">{document.title}</CardTitle>
            {getStatusBadge(document.status)}
          </div>
          <CardDescription>
            {project ? (
              <div className="space-y-1">
                <div>Project: {project.title}</div>
                <div className="text-xs text-muted-foreground">
                  Type: {document.type.charAt(0).toUpperCase() + document.type.slice(1)}
                </div>
              </div>
            ) : (
              <div>Project: {document.projectId}</div>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent className="pb-2">
          <div className="flex items-center text-sm text-muted-foreground">
            <Clock className="mr-1 h-4 w-4" />
            {document.lastModified ?
              `Last updated: ${format(new Date(document.lastModified), 'MMM d, yyyy')}` :
              'No update date'}
          </div>
        </CardContent>
        <CardFooter>
          <Button
            onClick={() => handleReviewDocument(document.id)}
            className="w-full"
          >
            Review Document
          </Button>
        </CardFooter>
      </Card>
    );
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex flex-col">
        <Header />
        <main className="flex-1 container py-6">
          <h1 className="text-2xl font-bold mb-6">Document Reviews</h1>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className="h-6 w-3/4" />
                  <Skeleton className="h-4 w-1/2 mt-2" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-4 w-full" />
                </CardContent>
                <CardFooter>
                  <Skeleton className="h-10 w-full" />
                </CardFooter>
              </Card>
            ))}
          </div>
        </main>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-background flex flex-col">
        <Header />
        <main className="flex-1 container py-6">
          <h1 className="text-2xl font-bold mb-6">Document Reviews</h1>
          <Card className="border-red-200 bg-red-50">
            <CardHeader>
              <CardTitle className="text-red-800 flex items-center">
                <AlertCircle className="mr-2 h-5 w-5" />
                Error Loading Reviews
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-red-700">
                There was a problem loading your document reviews. Please try again later.
              </p>
            </CardContent>
            <CardFooter>
              <Button variant="outline" onClick={() => window.location.reload()}>
                Retry
              </Button>
            </CardFooter>
          </Card>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <Header />
      <main className="flex-1 container py-6">
        <h1 className="text-2xl font-bold mb-6">Document Reviews</h1>

        <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="pending" className="flex items-center">
              <Clock className="mr-2 h-4 w-4" />
              Pending ({pendingReviews.length})
            </TabsTrigger>
            <TabsTrigger value="completed" className="flex items-center">
              <CheckCircle className="mr-2 h-4 w-4" />
              Approved ({completedReviews.length})
            </TabsTrigger>
            <TabsTrigger value="rejected" className="flex items-center">
              <AlertCircle className="mr-2 h-4 w-4" />
              Rejected ({rejectedReviews.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="pending" className="mt-6">
            {pendingReviews.length > 0 ? (
              <ScrollArea className="h-[calc(100vh-250px)]">
                {pendingReviews.map(renderDocumentCard)}
              </ScrollArea>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle>No Pending Reviews</CardTitle>
                  <CardDescription>
                    You don&apos;t have any documents waiting for review.
                  </CardDescription>
                </CardHeader>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="completed" className="mt-6">
            {completedReviews.length > 0 ? (
              <ScrollArea className="h-[calc(100vh-250px)]">
                {completedReviews.map(renderDocumentCard)}
              </ScrollArea>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle>No Approved Documents</CardTitle>
                  <CardDescription>
                    You haven&apos;t approved any documents yet.
                  </CardDescription>
                </CardHeader>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="rejected" className="mt-6">
            {rejectedReviews.length > 0 ? (
              <ScrollArea className="h-[calc(100vh-250px)]">
                {rejectedReviews.map(renderDocumentCard)}
              </ScrollArea>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle>No Rejected Documents</CardTitle>
                  <CardDescription>
                    You don&apos;t have any rejected documents.
                  </CardDescription>
                </CardHeader>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
}
