# Docker Deployment Guide

This guide explains how to deploy the Thesis Management System using Docker.

## Prerequisites

- Docker (version 20.10 or higher)
- Docker Compose (version 2.0 or higher)
- At least 4GB of available RAM
- At least 10GB of available disk space

## Quick Start

### 1. Environment Setup

Copy the environment template and configure your settings:

```bash
cp .env.docker .env
```

Edit the `.env` file and configure the following required variables:

```bash
# Required Configuration
APPWRITE_PROJECT_ID=your-project-id
APPWRITE_DATABASE_ID=your-database-id
_APP_OPENSSL_KEY_V1=your-openssl-key-here
_APP_EXECUTOR_SECRET=your-executor-secret-here
_APP_DB_PASS=your-secure-db-password
_APP_DB_ROOT_PASS=your-secure-root-password
```

### 2. Development Deployment

For development with hot reload:

```bash
./deploy.sh dev
```

This will:
- Build the application in development mode
- Start all services with hot reload enabled
- Make the app available at http://localhost:3000
- Make Appwrite console available at http://localhost:8080/console

### 3. Production Deployment

For production deployment:

```bash
./deploy.sh prod
```

This will:
- Build the application for production
- Start all services with Nginx reverse proxy
- Make the app available at http://localhost
- Enable production optimizations

## Services

The Docker setup includes the following services:

### Core Services

- **app**: Next.js application
- **appwrite**: Appwrite backend server
- **mariadb**: MySQL database
- **redis**: Redis cache
- **influxdb**: Usage statistics (optional)

### Production Services

- **nginx**: Reverse proxy and load balancer
- **certbot**: SSL certificate management

## Configuration

### Environment Variables

Key environment variables you should configure:

```bash
# Application
NODE_ENV=production
DOMAIN=your-domain.com

# Appwrite
APPWRITE_PROJECT_ID=your-project-id
APPWRITE_DATABASE_ID=your-database-id

# Security (generate with: openssl rand -base64 32)
_APP_OPENSSL_KEY_V1=your-secure-key
_APP_EXECUTOR_SECRET=your-secure-secret

# Database
_APP_DB_USER=appwrite
_APP_DB_PASS=your-secure-password
_APP_DB_ROOT_PASS=your-secure-root-password

# Email (optional)
_APP_SMTP_HOST=smtp.gmail.com
_APP_SMTP_PORT=587
_APP_SMTP_USERNAME=<EMAIL>
_APP_SMTP_PASSWORD=your-app-password
```

### SSL Configuration

To enable HTTPS in production:

1. Set up SSL certificates:
```bash
./deploy.sh ssl your-domain.com
```

2. Update `nginx.conf` to enable the HTTPS server block

3. Redeploy:
```bash
./deploy.sh prod
```

## Management Commands

### View Logs

```bash
# All services
./deploy.sh logs

# Specific service
./deploy.sh logs app
./deploy.sh logs appwrite
```

### Check Status

```bash
./deploy.sh status
```

### Stop Services

```bash
./deploy.sh stop
```

### Backup Data

```bash
./deploy.sh backup
```

### Cleanup

```bash
./deploy.sh cleanup
```

## File Structure

```
├── Dockerfile                 # Production build
├── Dockerfile.dev            # Development build
├── docker-compose.yml        # Base services
├── docker-compose.prod.yml   # Production overrides
├── docker-compose.dev.yml    # Development overrides
├── nginx.conf                # Nginx configuration
├── .env.docker              # Environment template
├── .dockerignore            # Docker ignore file
├── deploy.sh                # Deployment script
└── DOCKER_README.md         # This file
```

## Volumes

The following Docker volumes are created for data persistence:

- `appwrite-mariadb`: Database data
- `appwrite-redis`: Redis cache
- `appwrite-uploads`: File uploads
- `appwrite-certificates`: SSL certificates
- `appwrite-functions`: Appwrite functions
- `appwrite-config`: Appwrite configuration

## Networking

All services communicate through the `tms-network` Docker network.

### Port Mapping

**Development:**
- 3000: Next.js app
- 8080: Appwrite server

**Production:**
- 80: HTTP (Nginx)
- 443: HTTPS (Nginx)

## Troubleshooting

### Common Issues

1. **Port conflicts**: Ensure ports 3000, 8080, 80, and 443 are available
2. **Memory issues**: Ensure at least 4GB RAM is available
3. **Permission issues**: Ensure Docker daemon is running and user has permissions

### Debug Commands

```bash
# Check service logs
docker-compose logs -f [service-name]

# Access service shell
docker-compose exec [service-name] sh

# Check service status
docker-compose ps

# Restart specific service
docker-compose restart [service-name]
```

### Reset Everything

If you need to start fresh:

```bash
./deploy.sh cleanup
docker volume prune -f
./deploy.sh dev  # or prod
```

## Security Considerations

1. **Change default passwords**: Always use strong, unique passwords
2. **Generate secure keys**: Use `openssl rand -base64 32` for keys
3. **Enable HTTPS**: Use SSL certificates in production
4. **Firewall**: Configure firewall to only allow necessary ports
5. **Updates**: Regularly update Docker images and dependencies

## Performance Tuning

### For Production

1. **Resource limits**: Configure Docker resource limits
2. **Database tuning**: Optimize MariaDB configuration
3. **Nginx caching**: Enable appropriate caching headers
4. **CDN**: Consider using a CDN for static assets

### Monitoring

Consider adding monitoring services:
- Prometheus + Grafana for metrics
- ELK stack for log aggregation
- Health check endpoints

## Support

For issues related to:
- **Docker setup**: Check this README and Docker logs
- **Application issues**: Check application logs and documentation
- **Appwrite issues**: Check Appwrite documentation
