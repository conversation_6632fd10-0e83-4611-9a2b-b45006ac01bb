version: '3.8'

services:
  # Development version of the app with hot reload
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
      target: development
    container_name: tms-app-dev
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_APPWRITE_ENDPOINT=http://localhost:8080/v1
      - NEXT_PUBLIC_APPWRITE_PROJECT_ID=${APPWRITE_PROJECT_ID}
      - NEXT_PUBLIC_APPWRITE_DATABASE_ID=${APPWRITE_DATABASE_ID}
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - appwrite
    networks:
      - tms-network

  # Override appwrite for development
  appwrite:
    environment:
      - _APP_ENV=development
      - _APP_OPTIONS_FORCE_HTTPS=disabled
      - _APP_DOMAIN=http://localhost:8080
      - _APP_DOMAIN_TARGET=http://localhost:8080
