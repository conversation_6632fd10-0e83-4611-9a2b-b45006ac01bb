'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Document, DocumentStatus, DocumentType } from '@/lib/types';
import { DocumentWorkflowStatus } from '@/components/documents/DocumentWorkflowStatus';
import { useAuth } from '@/hooks/useAuth';
import { useDocument } from '@/hooks/useDocument';
import { AlertCircle, FileText, Upload, FolderOpen } from 'lucide-react';
import { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { useRouter } from 'next/navigation';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useToast } from '@/hooks/useToast';

const documentTypeLabels: Record<DocumentType, string> = {
  concept: 'Concept Paper',
  proposal: 'Thesis Proposal',
  thesis: 'Thesis',
};

const STATUS_COLORS: Record<DocumentStatus, string> = {
  approved: 'bg-green-500/10 text-green-500 hover:bg-green-500/20',
  rejected: 'bg-red-500/10 text-red-500 hover:bg-red-500/20',
  under_review: 'bg-yellow-500/10 text-yellow-500 hover:bg-yellow-500/20',
  draft: 'bg-gray-500/10 text-gray-500 hover:bg-gray-500/20',
};

// Document card component for better reusability
function DocumentCard({
  document,
  onClick,
  onDeleteClick
}: {
  document: Document;
  onClick: () => void;
  onDeleteClick?: (e: React.MouseEvent, id: string) => void;
}) {
  return (
    <Card
      className="hover:bg-muted/50 transition-colors cursor-pointer h-full relative"
      onClick={onClick}
    >
      {onDeleteClick && (
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-2 right-2 h-8 w-8 p-0 text-muted-foreground hover:text-destructive"
          onClick={(e) => onDeleteClick(e, document.id)}
          aria-label="Delete document"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M3 6h18"></path>
            <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
            <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
          </svg>
        </Button>
      )}
      <CardHeader className="flex flex-row items-center space-y-0 pb-2">
        <div className="flex-1">
          <CardTitle className="line-clamp-1">{document.title}</CardTitle>
          <div className="mt-1">
            <DocumentWorkflowStatus
              currentStatus={document.status as DocumentStatus}
              compact={true}
            />
          </div>
        </div>
        <FileText className="h-5 w-5 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Type:</span>
            <span>{documentTypeLabels[document.type as DocumentType]}</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Last Updated:</span>
            <span>{new Date(document.updatedAt).toLocaleDateString()}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Loading skeleton for documents
function DocumentsSkeleton() {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {Array(6).fill(0).map((_, index) => (
        <Card key={index} className="h-full">
          <CardHeader className="flex flex-row items-center space-y-0 pb-2">
            <div className="flex-1">
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-5 w-1/4 mt-1" />
            </div>
            <Skeleton className="h-5 w-5 rounded" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Skeleton className="h-4 w-1/4" />
                <Skeleton className="h-4 w-1/3" />
              </div>
              <div className="flex items-center justify-between">
                <Skeleton className="h-4 w-1/3" />
                <Skeleton className="h-4 w-1/4" />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

export default function DocumentsPage() {
  const { user } = useAuth();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<string | null>(null);
  const router = useRouter();
  const { toast } = useToast();

  // Use the useDocument hook instead of direct API calls
  const { getList, delete: deleteDocument } = useDocument();
  const {
    data: documents = [],
    isLoading,
    isError
  } = getList(
    user?.id ? { studentId: user.id } : undefined,
    {
      enabled: !!user,
      queryKey: ['documents', 'student', user?.id || '']
    }
  );

  const handleDeleteClick = (e: React.MouseEvent, documentId: string) => {
    e.stopPropagation(); // Prevent navigation when clicking delete
    setDocumentToDelete(documentId);
    setShowDeleteDialog(true);
  };

  const handleDeleteDocument = async () => {
    if (!documentToDelete) return;

    try {
      await deleteDocument.mutateAsync(documentToDelete);
      setShowDeleteDialog(false);
      setDocumentToDelete(null);
      toast({
        title: "Document deleted",
        description: "The document has been successfully deleted.",
      });
    } catch (error) {
      console.error('Error deleting document:', error);
      toast({
        title: "Error",
        description: "Failed to delete the document. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Loading state with skeleton
  if (isLoading) {
    return (
      <div className="py-2">
        <div className="flex items-center justify-between mb-6">
          <div>
            <Skeleton className="h-9 w-48" />
            <Skeleton className="h-5 w-72 mt-1" />
          </div>
          <Skeleton className="h-10 w-32" />
        </div>
        <DocumentsSkeleton />
      </div>
    );
  }

  // Error state
  if (isError) {
    return (
      <div className="py-2">
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            There was an error loading your documents. Please try again later.
          </AlertDescription>
        </Alert>
        <Button onClick={() => window.location.reload()} variant="outline">
          Retry
        </Button>
      </div>
    );
  }

  // Empty state
  if (documents.length === 0) {
    return (
      <div className="py-2">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">My Documents</h1>
            <p className="text-muted-foreground">
              Manage your concept papers, proposals, and theses
            </p>
          </div>
          {user?.role === 'student' && (
            <Button
              onClick={() => router.push('/documents/new')}
              className="flex items-center"
            >
              <Upload className="mr-2 h-4 w-4" />
              <span>New Document</span>
            </Button>
          )}
        </div>

        <div className="flex flex-col items-center justify-center py-12 text-center">
          <div className="rounded-full bg-muted p-3 mb-4">
            <FolderOpen className="h-10 w-10 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-semibold mb-1">
            {user?.role === 'student' ? 'No documents yet' : 'No documents available'}
          </h3>
          <p className="text-muted-foreground mb-4 max-w-md">
            {user?.role === 'student'
              ? "You haven't created any documents yet. Start by creating a new document."
              : "No documents are available for review at this time."
            }
          </p>
          {user?.role === 'student' && (
            <Button
              onClick={() => router.push('/documents/new')}
              className="flex items-center"
            >
              <Upload className="mr-2 h-4 w-4" />
              <span>New Document</span>
            </Button>
          )}
        </div>
      </div>
    );
  }

  // Main content with documents
  return (
    <div className="py-2">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">My Documents</h1>
          <p className="text-muted-foreground">
            Manage your concept papers, proposals, and theses
          </p>
        </div>
        {user?.role === 'student' && (
          <Button
            onClick={() => router.push('/documents/new')}
            className="flex items-center"
          >
            <Upload className="mr-2 h-4 w-4" />
            <span>New Document</span>
          </Button>
        )}
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {documents.map((doc) => (
          <DocumentCard
            key={doc.id}
            document={doc}
            onClick={() => router.push(`/documents/${doc.id}`)}
            onDeleteClick={handleDeleteClick}
          />
        ))}
      </div>

      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Document</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this document? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteDocument}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}








